Starting performance test [7eowxx] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 1/10: 50 signals, 213.760458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 2/10: 50 signals, 13.097958ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 3/10: 50 signals, 13.940875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 4/10: 50 signals, 16.217292ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 5/10: 50 signals, 3.609125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 6/10: 50 signals, 10.495541ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 7/10: 50 signals, 6.953708ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 8/10: 50 signals, 7.552792ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[7eowxx] Batch 9/10: 50 signals, 4.94175ms, success: false
[7eowxx] Batch 10/10: 50 signals, 800.534125ms, success: true

============================================================
PERFORMANCE TEST RESULTS [7eowxx]
============================================================
Total Requests:      10
Successful Batches:  1
Failed Batches:      9
Total Signals:       50
============================================================
TIMING METRICS:
Total Test Duration: 1.091223292s
Total Request Time:  1.091103624s
Average Latency:     1.091103624s
Min Latency:         3.609125ms
Max Latency:         800.534125ms
Latency Range:       796.925ms (221.8x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     9.16
Signals/Second:      45.82
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 9 batches failed
