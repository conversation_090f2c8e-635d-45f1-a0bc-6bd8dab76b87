Starting performance test [4avd8s] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[4avd8s] Batch 1/10: 50 signals, 826.369125ms, success: true
[4avd8s] Batch 2/10: 50 signals, 701.562708ms, success: true
[4avd8s] Batch 3/10: 50 signals, 897.401792ms, success: true
[4avd8s] Batch 4/10: 50 signals, 1.084669083s, success: true
[4avd8s] Batch 5/10: 50 signals, 1.094963834s, success: true
[4avd8s] Batch 6/10: 50 signals, 1.613364417s, success: true
[4avd8s] Batch 7/10: 50 signals, 1.5916465s, success: true
[4avd8s] Batch 8/10: 50 signals, 1.501141167s, success: true
[4avd8s] Batch 9/10: 50 signals, 1.555387708s, success: true
[4avd8s] Batch 10/10: 50 signals, 1.052610667s, success: true

============================================================
PERFORMANCE TEST RESULTS [4avd8s]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.920368209s
Total Request Time:  11.919117001s
Average Latency:     1.1919117s
Min Latency:         701.562708ms
Max Latency:         1.613364417s
Latency Range:       911.801709ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.84
Signals/Second:      41.95
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
