Starting performance test [4gwawk] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[4gwawk] Batch 1/10: 50 signals, 889.507416ms, success: true
[4gwawk] Batch 2/10: 50 signals, 721.961875ms, success: true
[4gwawk] Batch 3/10: 50 signals, 1.071680583s, success: true
[4gwawk] Batch 4/10: 50 signals, 930.903417ms, success: true
[4gwawk] Batch 5/10: 50 signals, 1.124075292s, success: true
[4gwawk] Batch 6/10: 50 signals, 1.6711115s, success: true
[4gwawk] Batch 7/10: 50 signals, 1.616955541s, success: true
[4gwawk] Batch 8/10: 50 signals, 1.505104667s, success: true
[4gwawk] Batch 9/10: 50 signals, 1.577643208s, success: true
[4gwawk] Batch 10/10: 50 signals, 1.238981625s, success: true

============================================================
PERFORMANCE TEST RESULTS [4gwawk]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.348530209s
Total Request Time:  12.347925124s
Average Latency:     1.234792512s
Min Latency:         721.961875ms
Max Latency:         1.6711115s
Latency Range:       949.149625ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.81
Signals/Second:      40.49
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
