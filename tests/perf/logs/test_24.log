Starting performance test [xgkunm] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 1/10: 50 signals, 188.026083ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 2/10: 50 signals, 9.882709ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 3/10: 50 signals, 13.564291ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 4/10: 50 signals, 19.689875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 5/10: 50 signals, 22.408458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 6/10: 50 signals, 5.529792ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 7/10: 50 signals, 3.38975ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 8/10: 50 signals, 17.01225ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 9/10: 50 signals, 6.745625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[xgkunm] Batch 10/10: 50 signals, 16.176834ms, success: false

============================================================
PERFORMANCE TEST RESULTS [xgkunm]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 302.533042ms
Total Request Time:  302.425667ms
Average Latency:     0s
Min Latency:         3.38975ms
Max Latency:         188.026083ms
Latency Range:       184.636333ms (55.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     33.05
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
