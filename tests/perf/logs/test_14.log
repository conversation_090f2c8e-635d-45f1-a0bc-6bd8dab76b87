Starting performance test [t1qbez] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 1/10: 50 signals, 213.274708ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 2/10: 50 signals, 10.630666ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 3/10: 50 signals, 31.656833ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 4/10: 50 signals, 7.03025ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 5/10: 50 signals, 3.639875ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 6/10: 50 signals, 8.746833ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 7/10: 50 signals, 7.654208ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 8/10: 50 signals, 6.693625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 9/10: 50 signals, 9.551166ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[t1qbez] Batch 10/10: 50 signals, 13.831625ms, success: false

============================================================
PERFORMANCE TEST RESULTS [t1qbez]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 312.922958ms
Total Request Time:  312.709789ms
Average Latency:     0s
Min Latency:         3.639875ms
Max Latency:         213.274708ms
Latency Range:       209.634833ms (58.6x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     31.96
Signals/Second:      0.00
Overhead Ratio:      0.1% (non-request time)
============================================================
❌ 10 batches failed
