Starting performance test [6t83jc] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[6t83jc] Batch 1/10: 50 signals, 698.386083ms, success: true
[6t83jc] Batch 2/10: 50 signals, 693.389958ms, success: true
[6t83jc] Batch 3/10: 50 signals, 760.770667ms, success: true
[6t83jc] Batch 4/10: 50 signals, 1.047364542s, success: true
[6t83jc] Batch 5/10: 50 signals, 1.110268917s, success: true
[6t83jc] Batch 6/10: 50 signals, 1.484164458s, success: true
[6t83jc] Batch 7/10: 50 signals, 1.608834583s, success: true
[6t83jc] Batch 8/10: 50 signals, 1.691838709s, success: true
[6t83jc] Batch 9/10: 50 signals, 1.420943625s, success: true
[6t83jc] Batch 10/10: 50 signals, 1.119551584s, success: true

============================================================
PERFORMANCE TEST RESULTS [6t83jc]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.636140958s
Total Request Time:  11.635513126s
Average Latency:     1.163551312s
Min Latency:         693.389958ms
Max Latency:         1.691838709s
Latency Range:       998.448751ms (2.4x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.86
Signals/Second:      42.97
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
