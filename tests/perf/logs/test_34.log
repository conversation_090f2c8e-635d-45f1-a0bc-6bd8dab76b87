Starting performance test [42jsu0] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[42jsu0] Batch 1/10: 50 signals, 197.580916ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 2/10: 50 signals, 26.874084ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 3/10: 50 signals, 19.794167ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 4/10: 50 signals, 9.631916ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 5/10: 50 signals, 5.642ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 6/10: 50 signals, 6.104458ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 7/10: 50 signals, 14.653917ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 8/10: 50 signals, 9.38475ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 9/10: 50 signals, 4.4925ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[42jsu0] Batch 10/10: 50 signals, 8.312708ms, success: false

============================================================
PERFORMANCE TEST RESULTS [42jsu0]
============================================================
Total Requests:      10
Successful Batches:  1
Failed Batches:      9
Total Signals:       50
============================================================
TIMING METRICS:
Total Test Duration: 302.599167ms
Total Request Time:  302.471416ms
Average Latency:     302.471416ms
Min Latency:         4.4925ms
Max Latency:         197.580916ms
Latency Range:       193.088416ms (44.0x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     33.05
Signals/Second:      165.24
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 9 batches failed
