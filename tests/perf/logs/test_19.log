Starting performance test [awli18] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[awli18] Batch 1/10: 50 signals, 703.124833ms, success: true
[awli18] Batch 2/10: 50 signals, 687.744834ms, success: true
[awli18] Batch 3/10: 50 signals, 797.279708ms, success: true
[awli18] Batch 4/10: 50 signals, 1.101904334s, success: true
[awli18] Batch 5/10: 50 signals, 1.032682542s, success: true
[awli18] Batch 6/10: 50 signals, 1.484951084s, success: true
[awli18] Batch 7/10: 50 signals, 1.582786167s, success: true
[awli18] Batch 8/10: 50 signals, 1.701283833s, success: true
[awli18] Batch 9/10: 50 signals, 1.197137708s, success: true
[awli18] Batch 10/10: 50 signals, 1.110215625s, success: true

============================================================
PERFORMANCE TEST RESULTS [awli18]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.399698208s
Total Request Time:  11.399110668s
Average Latency:     1.139911066s
Min Latency:         687.744834ms
Max Latency:         1.701283833s
Latency Range:       1.013538999s (2.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.88
Signals/Second:      43.86
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
