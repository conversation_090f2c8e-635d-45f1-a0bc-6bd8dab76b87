Starting performance test [9c8e9u] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[9c8e9u] Batch 1/10: 50 signals, 487.825584ms, success: true
[9c8e9u] Batch 2/10: 50 signals, 816.461792ms, success: true
[9c8e9u] Batch 3/10: 50 signals, 737.029417ms, success: true
[9c8e9u] Batch 4/10: 50 signals, 1.057147375s, success: true
[9c8e9u] Batch 5/10: 50 signals, 1.024835375s, success: true
[9c8e9u] Batch 6/10: 50 signals, 1.489625333s, success: true
[9c8e9u] Batch 7/10: 50 signals, 1.586528625s, success: true
[9c8e9u] Batch 8/10: 50 signals, 1.635455125s, success: true
[9c8e9u] Batch 9/10: 50 signals, 1.467540916s, success: true
[9c8e9u] Batch 10/10: 50 signals, 1.327214083s, success: true

============================================================
PERFORMANCE TEST RESULTS [9c8e9u]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.630143375s
Total Request Time:  11.629663625s
Average Latency:     1.162966362s
Min Latency:         487.825584ms
Max Latency:         1.635455125s
Latency Range:       1.147629541s (3.4x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.86
Signals/Second:      42.99
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
