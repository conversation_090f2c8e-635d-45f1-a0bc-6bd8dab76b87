Starting performance test [lvbda7] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[lvbda7] Batch 1/10: 50 signals, 792.425292ms, success: true
[lvbda7] Batch 2/10: 50 signals, 696.598625ms, success: true
[lvbda7] Batch 3/10: 50 signals, 825.648541ms, success: true
[lvbda7] Batch 4/10: 50 signals, 1.090318833s, success: true
[lvbda7] Batch 5/10: 50 signals, 1.182973708s, success: true
[lvbda7] Batch 6/10: 50 signals, 1.627333333s, success: true
[lvbda7] Batch 7/10: 50 signals, 1.595149s, success: true
[lvbda7] Batch 8/10: 50 signals, 1.721411208s, success: true
[lvbda7] Batch 9/10: 50 signals, 1.335363875s, success: true
[lvbda7] Batch 10/10: 50 signals, 1.335886834s, success: true

============================================================
PERFORMANCE TEST RESULTS [lvbda7]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.203490084s
Total Request Time:  12.203109249s
Average Latency:     1.220310924s
Min Latency:         696.598625ms
Max Latency:         1.721411208s
Latency Range:       1.024812583s (2.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.82
Signals/Second:      40.97
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
