Starting performance test [1n758u] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[1n758u] Batch 1/10: 50 signals, 127.723959ms, success: true
[1n758u] Batch 2/10: 50 signals, 274.319625ms, success: true
[1n758u] Batch 3/10: 50 signals, 809.32275ms, success: true
[1n758u] Batch 4/10: 50 signals, 725.430167ms, success: true
[1n758u] Batch 5/10: 50 signals, 1.0492255s, success: true
[1n758u] Batch 6/10: 50 signals, 903.292917ms, success: true
[1n758u] Batch 7/10: 50 signals, 1.342779334s, success: true
[1n758u] Batch 8/10: 50 signals, 1.491653833s, success: true
[1n758u] Batch 9/10: 50 signals, 1.5074665s, success: true
[1n758u] Batch 10/10: 50 signals, 1.563198625s, success: true

============================================================
PERFORMANCE TEST RESULTS [1n758u]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 9.795128s
Total Request Time:  9.79441321s
Average Latency:     979.441321ms
Min Latency:         127.723959ms
Max Latency:         1.563198625s
Latency Range:       1.435474666s (12.2x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.02
Signals/Second:      51.05
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
