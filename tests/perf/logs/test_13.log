Starting performance test [z8mc2j] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 1/10: 50 signals, 182.756292ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 2/10: 50 signals, 7.844709ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 3/10: 50 signals, 15.678625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 4/10: 50 signals, 9.207625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 5/10: 50 signals, 33.805917ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 6/10: 50 signals, 4.292583ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 7/10: 50 signals, 3.711916ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 8/10: 50 signals, 3.622959ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 9/10: 50 signals, 16.513708ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[z8mc2j] Batch 10/10: 50 signals, 6.391791ms, success: false

============================================================
PERFORMANCE TEST RESULTS [z8mc2j]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 283.916542ms
Total Request Time:  283.826125ms
Average Latency:     0s
Min Latency:         3.622959ms
Max Latency:         182.756292ms
Latency Range:       179.133333ms (50.4x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     35.22
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
