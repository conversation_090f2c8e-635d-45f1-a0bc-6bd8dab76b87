Starting performance test [ek70us] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[ek70us] Batch 1/10: 50 signals, 833.142916ms, success: true
[ek70us] Batch 2/10: 50 signals, 701.5375ms, success: true
[ek70us] Batch 3/10: 50 signals, 886.011834ms, success: true
[ek70us] Batch 4/10: 50 signals, 1.082111292s, success: true
[ek70us] Batch 5/10: 50 signals, 1.095798625s, success: true
[ek70us] Batch 6/10: 50 signals, 1.627398208s, success: true
[ek70us] Batch 7/10: 50 signals, 1.594009125s, success: true
[ek70us] Batch 8/10: 50 signals, 1.725138791s, success: true
[ek70us] Batch 9/10: 50 signals, 1.569164084s, success: true
[ek70us] Batch 10/10: 50 signals, 1.092998709s, success: true

============================================================
PERFORMANCE TEST RESULTS [ek70us]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.207806084s
Total Request Time:  12.207311084s
Average Latency:     1.220731108s
Min Latency:         701.5375ms
Max Latency:         1.725138791s
Latency Range:       1.023601291s (2.5x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.82
Signals/Second:      40.96
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
