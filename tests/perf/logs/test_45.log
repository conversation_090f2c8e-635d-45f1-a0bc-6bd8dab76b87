Starting performance test [bss0j0] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 1/10: 50 signals, 181.768167ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 2/10: 50 signals, 11.595042ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 3/10: 50 signals, 28.159792ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 4/10: 50 signals, 6.859125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 5/10: 50 signals, 6.313666ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 6/10: 50 signals, 7.206417ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 7/10: 50 signals, 11.814583ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 8/10: 50 signals, 6.931416ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 9/10: 50 signals, 10.211333ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[bss0j0] Batch 10/10: 50 signals, 10.42125ms, success: false

============================================================
PERFORMANCE TEST RESULTS [bss0j0]
============================================================
Total Requests:      10
Successful Batches:  0
Failed Batches:      10
Total Signals:       0
============================================================
TIMING METRICS:
Total Test Duration: 281.355625ms
Total Request Time:  281.280791ms
Average Latency:     0s
Min Latency:         6.313666ms
Max Latency:         181.768167ms
Latency Range:       175.454501ms (28.8x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     35.54
Signals/Second:      0.00
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 10 batches failed
