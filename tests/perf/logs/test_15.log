Starting performance test [jq5prd] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[jq5prd] Batch 1/10: 50 signals, 704.265208ms, success: true
[jq5prd] Batch 2/10: 50 signals, 692.386541ms, success: true
[jq5prd] Batch 3/10: 50 signals, 794.204916ms, success: true
[jq5prd] Batch 4/10: 50 signals, 1.098230208s, success: true
[jq5prd] Batch 5/10: 50 signals, 1.028078917s, success: true
[jq5prd] Batch 6/10: 50 signals, 1.477804416s, success: true
[jq5prd] Batch 7/10: 50 signals, 1.595259416s, success: true
[jq5prd] Batch 8/10: 50 signals, 1.506790042s, success: true
[jq5prd] Batch 9/10: 50 signals, 1.400932s, success: true
[jq5prd] Batch 10/10: 50 signals, 1.342724125s, success: true

============================================================
PERFORMANCE TEST RESULTS [jq5prd]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.641187208s
Total Request Time:  11.640675789s
Average Latency:     1.164067578s
Min Latency:         692.386541ms
Max Latency:         1.595259416s
Latency Range:       902.872875ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.86
Signals/Second:      42.95
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
