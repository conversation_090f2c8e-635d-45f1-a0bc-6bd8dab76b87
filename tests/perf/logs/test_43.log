Starting performance test [tbqdu5] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[tbqdu5] Batch 1/10: 50 signals, 338.48125ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[tbqdu5] Batch 2/10: 50 signals, 6.404ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[tbqdu5] Batch 3/10: 50 signals, 3.399625ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[tbqdu5] Batch 4/10: 50 signals, 3.177834ms, success: false
[tbqdu5] Batch 5/10: 50 signals, 839.861ms, success: true
[tbqdu5] Batch 6/10: 50 signals, 702.36275ms, success: true
[tbqdu5] Batch 7/10: 50 signals, 1.087415709s, success: true
[tbqdu5] Batch 8/10: 50 signals, 1.027349208s, success: true
[tbqdu5] Batch 9/10: 50 signals, 1.411822833s, success: true
[tbqdu5] Batch 10/10: 50 signals, 1.512921375s, success: true

============================================================
PERFORMANCE TEST RESULTS [tbqdu5]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 6.933628625s
Total Request Time:  6.933195584s
Average Latency:     990.456512ms
Min Latency:         3.177834ms
Max Latency:         1.512921375s
Latency Range:       1.509743541s (476.1x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.44
Signals/Second:      50.48
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
