Starting performance test [x1613f] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[x1613f] Batch 1/10: 50 signals, 777.8795ms, success: true
[x1613f] Batch 2/10: 50 signals, 716.638041ms, success: true
[x1613f] Batch 3/10: 50 signals, 823.208583ms, success: true
[x1613f] Batch 4/10: 50 signals, 1.062805625s, success: true
[x1613f] Batch 5/10: 50 signals, 1.041810416s, success: true
[x1613f] Batch 6/10: 50 signals, 1.645880334s, success: true
[x1613f] Batch 7/10: 50 signals, 1.432512792s, success: true
[x1613f] Batch 8/10: 50 signals, 1.579432625s, success: true
[x1613f] Batch 9/10: 50 signals, 1.416624208s, success: true
[x1613f] Batch 10/10: 50 signals, 1.395990125s, success: true

============================================================
PERFORMANCE TEST RESULTS [x1613f]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.893235458s
Total Request Time:  11.892782249s
Average Latency:     1.189278224s
Min Latency:         716.638041ms
Max Latency:         1.645880334s
Latency Range:       929.242293ms (2.3x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.84
Signals/Second:      42.04
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
