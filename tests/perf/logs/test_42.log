Starting performance test [rdu3bg] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[rdu3bg] Batch 1/10: 50 signals, 124.3285ms, success: true
[rdu3bg] Batch 2/10: 50 signals, 278.451291ms, success: true
[rdu3bg] Batch 3/10: 50 signals, 831.651959ms, success: true
[rdu3bg] Batch 4/10: 50 signals, 750.8275ms, success: true
[rdu3bg] Batch 5/10: 50 signals, 1.096805792s, success: true
[rdu3bg] Batch 6/10: 50 signals, 1.044654375s, success: true
[rdu3bg] Batch 7/10: 50 signals, 1.496447709s, success: true
[rdu3bg] Batch 8/10: 50 signals, 1.566754167s, success: true
[rdu3bg] Batch 9/10: 50 signals, 1.440468625s, success: true
[rdu3bg] Batch 10/10: 50 signals, 1.389150542s, success: true

============================================================
PERFORMANCE TEST RESULTS [rdu3bg]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 10.019977833s
Total Request Time:  10.01954046s
Average Latency:     1.001954046s
Min Latency:         124.3285ms
Max Latency:         1.566754167s
Latency Range:       1.442425667s (12.6x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.00
Signals/Second:      49.90
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
