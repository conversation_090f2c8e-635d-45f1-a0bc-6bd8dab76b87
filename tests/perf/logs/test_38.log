Starting performance test [87rroi] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[87rroi] Batch 1/10: 50 signals, 289.249792ms, success: true
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[87rroi] Batch 2/10: 50 signals, 9.165417ms, success: false
[87rroi] Batch 3/10: 50 signals, 791.418917ms, success: true
[87rroi] Batch 4/10: 50 signals, 709.773333ms, success: true
[87rroi] Batch 5/10: 50 signals, 1.027637959s, success: true
[87rroi] Batch 6/10: 50 signals, 907.579458ms, success: true
[87rroi] Batch 7/10: 50 signals, 1.496253542s, success: true
[87rroi] Batch 8/10: 50 signals, 1.5050285s, success: true
[87rroi] Batch 9/10: 50 signals, 1.696813708s, success: true
[87rroi] Batch 10/10: 50 signals, 1.366078125s, success: true

============================================================
PERFORMANCE TEST RESULTS [87rroi]
============================================================
Total Requests:      10
Successful Batches:  9
Failed Batches:      1
Total Signals:       450
============================================================
TIMING METRICS:
Total Test Duration: 9.799368875s
Total Request Time:  9.798998751s
Average Latency:     1.088777639s
Min Latency:         9.165417ms
Max Latency:         1.696813708s
Latency Range:       1.687648291s (185.1x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.02
Signals/Second:      45.92
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 1 batches failed
