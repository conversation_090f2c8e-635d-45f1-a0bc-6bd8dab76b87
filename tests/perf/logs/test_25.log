Starting performance test [g6kcdc] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[g6kcdc] Batch 1/10: 50 signals, 406.219167ms, success: true
[g6kcdc] Batch 2/10: 50 signals, 810.351291ms, success: true
[g6kcdc] Batch 3/10: 50 signals, 774.474541ms, success: true
[g6kcdc] Batch 4/10: 50 signals, 1.095091208s, success: true
[g6kcdc] Batch 5/10: 50 signals, 925.856333ms, success: true
[g6kcdc] Batch 6/10: 50 signals, 1.41349575s, success: true
[g6kcdc] Batch 7/10: 50 signals, 1.501355917s, success: true
[g6kcdc] Batch 8/10: 50 signals, 1.699795542s, success: true
[g6kcdc] Batch 9/10: 50 signals, 1.396276916s, success: true
[g6kcdc] Batch 10/10: 50 signals, 1.374707709s, success: true

============================================================
PERFORMANCE TEST RESULTS [g6kcdc]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 11.398150875s
Total Request Time:  11.397624374s
Average Latency:     1.139762437s
Min Latency:         406.219167ms
Max Latency:         1.699795542s
Latency Range:       1.293576375s (4.2x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.88
Signals/Second:      43.87
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
