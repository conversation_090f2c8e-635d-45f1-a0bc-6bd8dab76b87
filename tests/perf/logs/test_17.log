Starting performance test [inkna3] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[inkna3] Batch 1/10: 50 signals, 218.074ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[inkna3] Batch 2/10: 50 signals, 33.081125ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[inkna3] Batch 3/10: 50 signals, 7.069458ms, success: false
[inkna3] Batch 4/10: 50 signals, 748.693ms, success: true
[inkna3] Batch 5/10: 50 signals, 791.914208ms, success: true
[inkna3] Batch 6/10: 50 signals, 1.034826625s, success: true
[inkna3] Batch 7/10: 50 signals, 1.056088958s, success: true
[inkna3] Batch 8/10: 50 signals, 1.341379875s, success: true
[inkna3] Batch 9/10: 50 signals, 1.500248291s, success: true
[inkna3] Batch 10/10: 50 signals, 1.680185083s, success: true

============================================================
PERFORMANCE TEST RESULTS [inkna3]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 8.411856375s
Total Request Time:  8.411560623s
Average Latency:     1.201651517s
Min Latency:         7.069458ms
Max Latency:         1.680185083s
Latency Range:       1.673115625s (237.7x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.19
Signals/Second:      41.61
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
