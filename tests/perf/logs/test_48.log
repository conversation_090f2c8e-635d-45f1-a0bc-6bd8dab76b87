Starting performance test [i8n24e] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
[i8n24e] Batch 1/10: 50 signals, 817.436ms, success: true
[i8n24e] Batch 2/10: 50 signals, 761.631791ms, success: true
[i8n24e] Batch 3/10: 50 signals, 826.75875ms, success: true
[i8n24e] Batch 4/10: 50 signals, 1.083352834s, success: true
[i8n24e] Batch 5/10: 50 signals, 1.204272125s, success: true
[i8n24e] Batch 6/10: 50 signals, 1.685977s, success: true
[i8n24e] Batch 7/10: 50 signals, 1.619473791s, success: true
[i8n24e] Batch 8/10: 50 signals, 1.515015833s, success: true
[i8n24e] Batch 9/10: 50 signals, 1.578313041s, success: true
[i8n24e] Batch 10/10: 50 signals, 1.231420416s, success: true

============================================================
PERFORMANCE TEST RESULTS [i8n24e]
============================================================
Total Requests:      10
Successful Batches:  10
Failed Batches:      0
Total Signals:       500
============================================================
TIMING METRICS:
Total Test Duration: 12.326642417s
Total Request Time:  12.323651581s
Average Latency:     1.232365158s
Min Latency:         761.631791ms
Max Latency:         1.685977s
Latency Range:       924.345209ms (2.2x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     0.81
Signals/Second:      40.56
Overhead Ratio:      0.0% (non-request time)
============================================================
✅ All batches processed successfully
