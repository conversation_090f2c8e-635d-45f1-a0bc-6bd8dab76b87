Starting performance test [j3w8eb] with 10 batches of 50 signals each
Target: http://localhost:8080/api/isn/perf-test/signal_types/perf-test-validated/v1.0.0/signals
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[j3w8eb] Batch 1/10: 50 signals, 206.461166ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[j3w8eb] Batch 2/10: 50 signals, 8.284166ms, success: false
Unexpected status code: 429, Response: {"error_code":"rate_limit_exceeded","message":"Rate limit exceeded"}
[j3w8eb] Batch 3/10: 50 signals, 10.986ms, success: false
[j3w8eb] Batch 4/10: 50 signals, 714.276125ms, success: true
[j3w8eb] Batch 5/10: 50 signals, 760.531917ms, success: true
[j3w8eb] Batch 6/10: 50 signals, 982.99775ms, success: true
[j3w8eb] Batch 7/10: 50 signals, 936.628792ms, success: true
[j3w8eb] Batch 8/10: 50 signals, 1.285141333s, success: true
[j3w8eb] Batch 9/10: 50 signals, 1.623804833s, success: true
[j3w8eb] Batch 10/10: 50 signals, 1.674012084s, success: true

============================================================
PERFORMANCE TEST RESULTS [j3w8eb]
============================================================
Total Requests:      10
Successful Batches:  7
Failed Batches:      3
Total Signals:       350
============================================================
TIMING METRICS:
Total Test Duration: 8.206557625s
Total Request Time:  8.203124166s
Average Latency:     1.17187488s
Min Latency:         8.284166ms
Max Latency:         1.674012084s
Latency Range:       1.665727918s (202.1x slower)
============================================================
THROUGHPUT METRICS:
Requests/Second:     1.22
Signals/Second:      42.65
Overhead Ratio:      0.0% (non-request time)
============================================================
❌ 3 batches failed
